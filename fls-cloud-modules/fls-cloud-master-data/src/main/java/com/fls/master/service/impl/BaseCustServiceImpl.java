package com.fls.master.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fls.common.core.constant.CommonConstants;
import com.fls.common.core.exception.ServiceException;
import com.fls.master.convert.BaseCustConvert;
import com.fls.master.entity.BaseAddressEntity;
import com.fls.master.entity.BaseCustEntity;
import com.fls.master.entity.BaseLinkmanEntity;
import com.fls.master.entity.CustAddressEntity;
import com.fls.master.entity.CustLinkmanEntity;
import com.fls.master.mapper.BaseCustMapper;
import com.fls.master.mapper.CustAddressMapper;
import com.fls.master.mapper.CustLinkmanMapper;
import com.fls.master.pojo.builer.CustWrapperBuilder;
import com.fls.master.pojo.query.ArchiveQuery;
import com.fls.master.pojo.query.BaseCustQuery;
import com.fls.master.pojo.query.CustAllocationQuery;
import com.fls.master.pojo.vo.BaseCustDetailVO;
import com.fls.master.pojo.vo.BaseCustVO;
import com.fls.master.pojo.vo.CustAddressVO;
import com.fls.master.pojo.vo.CustAllocationVO;
import com.fls.master.pojo.vo.CustLinkmanVO;
import com.fls.master.pojo.vo.CustServiceUnitVO;
import com.fls.master.pojo.vo.PageResult;
import com.fls.master.service.BaseCustService;
import com.fls.master.service.PermissionService;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 客户基本档案-service
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
@Service
@Slf4j
@SuppressWarnings("unchecked")
public class BaseCustServiceImpl extends MPJBaseServiceImpl<BaseCustMapper, BaseCustEntity> implements BaseCustService {

    @Resource
    private PermissionService permissionService;

    @Resource
    private CustAddressMapper custAddressMapper;

    @Resource
    private CustLinkmanMapper custLinkmanMapper;

    @Override
    public PageResult<BaseCustVO> page(ArchiveQuery query) {
        BaseCustQuery tansQuery = (BaseCustQuery) query;
        Page<BaseCustVO> page = new Page<>(tansQuery.getPageNo(), tansQuery.getPageSize());
        MPJLambdaWrapper<BaseCustEntity> wrapper = CustWrapperBuilder.builder().withCommonConditions(tansQuery).withQueryParameters(tansQuery).build();
        Page<BaseCustVO> baseCustVOPage = baseMapper.selectJoinPage(page, BaseCustVO.class, wrapper);
        return new PageResult<>(baseCustVOPage, baseCustVOPage.getRecords());
    }

    @Override
    public List<BaseCustVO> queryAuthArchives(ArchiveQuery query) {
        BaseCustQuery tansQuery = (BaseCustQuery) query;
        List<String> privateArhIds = permissionService.getPrivateOrgId(query.getUserId(), query.getHref());
        if (CollectionUtil.isEmpty(privateArhIds)) {
            return Collections.emptyList();
        }
        MPJLambdaWrapper<BaseCustEntity> wrapper =
            CustWrapperBuilder.builder().withCommonConditions(tansQuery).withQueryParameters(tansQuery).withAuthorization(privateArhIds).build();
        return baseMapper.selectJoinList(BaseCustVO.class, wrapper);
    }

    @Override
    public BaseCustDetailVO queryArchiveDetail(String archiveId) {
        BaseCustEntity entity = baseMapper.selectById(archiveId);
        return BaseCustConvert.INSTANCE.entityToDetailVo(entity);
    }

    @Override
    public List<CustAddressVO> queryCustAddress(String custId) {
        MPJLambdaWrapper<CustAddressEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(CustAddressEntity.class)
            .selectAs(BaseAddressEntity::getDetail, CustAddressVO::getDetail)
            .innerJoin(BaseAddressEntity.class, BaseAddressEntity::getIdAddress, CustAddressEntity::getIdAddress)
            .eq(CustAddressEntity::getIdCustomer, custId)
            .eq(CustAddressEntity::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED);
        return custAddressMapper.selectJoinList(CustAddressVO.class, wrapper);
    }

    @Override
    public List<CustLinkmanVO> queryCustLinkman(String custId) {
        MPJLambdaWrapper<CustLinkmanEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.select(CustLinkmanEntity::getIdCustlinkman, CustLinkmanEntity::getIdLinkman, CustLinkmanEntity::getIdCustomer, CustLinkmanEntity::getPkCustlinkman)
            .select(BaseLinkmanEntity::getName, BaseLinkmanEntity::getIdAppellation, BaseLinkmanEntity::getSex, BaseLinkmanEntity::getTel, BaseLinkmanEntity::getMobile,
                BaseLinkmanEntity::getEmail, BaseLinkmanEntity::getFax, BaseLinkmanEntity::getAddress, BaseLinkmanEntity::getPkLinkman)
            .innerJoin(BaseLinkmanEntity.class, BaseLinkmanEntity::getIdLinkman, CustLinkmanEntity::getIdLinkman)
            .eq(CustLinkmanEntity::getIdCustomer, custId)
            .eq(CustLinkmanEntity::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED);
        return custLinkmanMapper.selectJoinList(CustLinkmanVO.class, wrapper);
    }

    @Override
    public PageResult<CustAllocationVO> queryCustAllocation(CustAllocationQuery query) {
        List<String> privateOrgId = permissionService.getPrivateOrgId(query.getUserId(), query.getHref());
        // 如果用户没有任何组织权限，直接返回空结果
        if (CollUtil.isEmpty(privateOrgId)) {
            return new PageResult<>(new Page<>(query.getPageNo(), query.getPageSize()), Collections.emptyList());
        }
        // 如果指定了查询组织，验证权限并过滤
        if (StrUtil.isNotEmpty(query.getIdOrg())) {
            if (!privateOrgId.contains(query.getIdOrg())) {
                throw new ServiceException("查询组织无权限");
            }
            privateOrgId = Collections.singletonList(query.getIdOrg());
        }
        // 执行分页查询
        Page<CustAllocationVO> page = new Page<>(query.getPageNo(), query.getPageSize());
        Page<CustAllocationVO> allocPage = custAddressMapper.selectAllocPAge(query, privateOrgId, page);
        return new PageResult<>(page, allocPage.getRecords());
    }

    @Override
    public List<CustServiceUnitVO> queryCustService(String id) {
        return Collections.emptyList();
    }
}

package com.fls.master.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fls.common.core.constant.CommonConstants;
import com.fls.common.core.exception.ServiceException;
import com.fls.master.convert.BaseSupplierConvert;
import com.fls.master.entity.BaseAddressEntity;
import com.fls.master.entity.BaseLinkmanEntity;
import com.fls.master.entity.BaseSupplierEntity;
import com.fls.master.entity.SuppAddressEntity;
import com.fls.master.entity.SupplierLinkmanEntity;
import com.fls.master.mapper.BaseSupplierMapper;
import com.fls.master.mapper.NcMapper;
import com.fls.master.mapper.SupplierAddressMapper;
import com.fls.master.mapper.SupplierLinkmanMapper;
import com.fls.master.pojo.builer.SupplierWrapperBuilder;
import com.fls.master.pojo.query.ArchiveQuery;
import com.fls.master.pojo.query.BaseSupplierQuery;
import com.fls.master.pojo.vo.BaseSupplierDetailVO;
import com.fls.master.pojo.vo.BaseSupplierVO;
import com.fls.master.pojo.vo.PageResult;
import com.fls.master.pojo.vo.SupplierAddressVO;
import com.fls.master.pojo.vo.SupplierLinkmanVO;
import com.fls.master.pojo.vo.SupplierTaxRateVO;
import com.fls.master.service.BaseSupplierService;
import com.fls.master.service.PermissionService;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 供应商基本档案-service
 *
 * <AUTHOR>
 * @since 2024-11-07
 */
@Service
@Slf4j
@SuppressWarnings("unchecked")
public class BaseSupplierServiceImpl extends MPJBaseServiceImpl<BaseSupplierMapper, BaseSupplierEntity> implements BaseSupplierService {

    @Resource
    private PermissionService permissionService;

    @Resource
    private SupplierAddressMapper supplierAddressMapper;

    @Resource
    private SupplierLinkmanMapper supplierLinkmanMapper;

    @Resource
    private NcMapper ncMapper;

    @Override
    public PageResult<BaseSupplierVO> page(ArchiveQuery query) {
        BaseSupplierQuery tansQuery = (BaseSupplierQuery) query;
        Page<BaseSupplierVO> page = new Page<>(tansQuery.getPageNo(), tansQuery.getPageSize());
        MPJLambdaWrapper<BaseSupplierEntity> wrapper = SupplierWrapperBuilder.builder().withCommonConditions(tansQuery).withQueryParameters(tansQuery).build();
        Page<BaseSupplierVO> baseCustVOPage = baseMapper.selectJoinPage(page, BaseSupplierVO.class, wrapper);
        return new PageResult<>(baseCustVOPage, baseCustVOPage.getRecords());
    }

    @Override
    public List<BaseSupplierVO> queryAuthArchives(ArchiveQuery query) {
        BaseSupplierQuery tansQuery = (BaseSupplierQuery) query;
        List<String> privateArhIds = permissionService.getPrivateOrgId(query.getUserId(), query.getHref());
        if (CollectionUtil.isEmpty(privateArhIds)) {
            return Collections.emptyList();
        }
        MPJLambdaWrapper<BaseSupplierEntity> wrapper = SupplierWrapperBuilder.builder().withCommonConditions(tansQuery).withQueryParameters(tansQuery).withAuthorization(privateArhIds).build();
        return baseMapper.selectJoinList(BaseSupplierVO.class, wrapper);
    }

    @Override
    public BaseSupplierDetailVO queryArchiveDetail(String archiveId) {
        BaseSupplierEntity entity = baseMapper.selectById(archiveId);
        return BaseSupplierConvert.INSTANCE.entityToDetailVo(entity);
    }

    @Override
    public List<SupplierAddressVO> querySupplierAddress(String supplierId) {
        MPJLambdaWrapper<SuppAddressEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(SuppAddressEntity.class)
            .selectAs(BaseAddressEntity::getDetail, SupplierAddressVO::getDetail)
            .innerJoin(BaseAddressEntity.class, BaseAddressEntity::getIdAddress, SuppAddressEntity::getIdAddress)
            .eq(SuppAddressEntity::getIdSupplier, supplierId)
            .eq(SuppAddressEntity::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED);
        return supplierAddressMapper.selectJoinList(SupplierAddressVO.class, wrapper);
    }

    @Override
    public List<SupplierLinkmanVO> querySupplierLinkman(String supplierId) {
        MPJLambdaWrapper<SupplierLinkmanEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.select(SupplierLinkmanEntity::getIdSupplierlinkman, SupplierLinkmanEntity::getIdLinkman, SupplierLinkmanEntity::getIdSupplier, SupplierLinkmanEntity::getPkSuplinkman)
            .select(BaseLinkmanEntity::getName, BaseLinkmanEntity::getIdAppellation, BaseLinkmanEntity::getSex, BaseLinkmanEntity::getTel, BaseLinkmanEntity::getMobile, BaseLinkmanEntity::getEmail, BaseLinkmanEntity::getFax, BaseLinkmanEntity::getAddress, BaseLinkmanEntity::getPkLinkman)
            .innerJoin(BaseLinkmanEntity.class, BaseLinkmanEntity::getIdLinkman, SupplierLinkmanEntity::getIdLinkman)
            .eq(SupplierLinkmanEntity::getIdSupplier, supplierId)
            .eq(SupplierLinkmanEntity::getDeleteFlag, CommonConstants.DELETE_FLAG_NOT_DELETED);
        return supplierLinkmanMapper.selectJoinList(SupplierLinkmanVO.class, wrapper);
    }

    @Override
    public SupplierTaxRateVO querySupplierTaxRate(String id) {
        // 校验供应商id不能为空
        if (StrUtil.isBlank(id)) {
            throw new ServiceException("供应商id不能为空");
        }
        // 按照id查询供应商信息
        BaseSupplierEntity supplier = getById(id);
        if (supplier == null) {
            throw new ServiceException("供应商信息不存在");
        }
        // 用pk查询供应商税率
        SupplierTaxRateVO supplerTaxRate = ncMapper.getSupplerTaxRate(supplier.getPkSupplier());
        if (supplerTaxRate == null) {
            return new SupplierTaxRateVO();
        }
        // 设置供应商id到税率VO中
        supplerTaxRate.setIdSupplier(id);
        // 返回响应结果
        return supplerTaxRate;
    }
}

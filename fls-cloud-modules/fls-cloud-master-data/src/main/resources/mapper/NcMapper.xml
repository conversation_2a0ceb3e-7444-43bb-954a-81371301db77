<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fls.master.mapper.NcMapper">

    <select id="getSupplerTaxRate" resultType="com.fls.master.pojo.vo.SupplierTaxRateVO"
            parameterType="java.lang.String">
        select sp.pk_supplier, sp.code, sp.name, tr.taxrate
        from bd_supplier sp
        left join bd_suptaxes spt on spt.pk_suptaxes =sp.pk_suptaxes
        left join bd_taxcode tc on tc.suptaxes =spt.pk_suptaxes
        left join bd_taxrate tr on tr.pk_taxcode =tc.pk_taxcode
        where tc.mattaxes='1001Z01000000003W0WH'
        <if test="pkSupplier != null and pkSupplier != ''">
            AND sp.pk_supplier = #{pkSupplier}
        </if>
        and tr.begindate in (select max(tr.begindate)
        from bd_supplier sp
        left join bd_suptaxes spt on spt.pk_suptaxes =sp.pk_suptaxes
        left join bd_taxcode tc on tc.suptaxes =spt.pk_suptaxes
        left join bd_taxrate tr on tr.pk_taxcode =tc.pk_taxcode
        where tc.mattaxes='1001Z01000000003W0WH'
        <if test="pkSupplier != null and pkSupplier != ''">
            AND sp.pk_supplier = #{pkSupplier}
        </if>
        )
    </select>
</mapper>
